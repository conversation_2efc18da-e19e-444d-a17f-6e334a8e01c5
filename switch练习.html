<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Switch语句练习 - 成绩等级判断</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .result {
            background-color: #e6f3ff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007acc;
        }
        .error {
            background-color: #ffe6e6;
            border-left-color: #ff4444;
        }
        .success {
            background-color: #e6ffe6;
            border-left-color: #44ff44;
        }
        .code-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .code-block {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        .highlight {
            color: #007acc;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Switch语句练习 - 成绩等级判断</h1>

        <script>
            // 获取用户输入并转换为数字
            var score = prompt("请输入你的成绩（0-100）:");
            var numScore = parseFloat(score);

            // 验证输入
            if (isNaN(numScore) || numScore < 0 || numScore > 100) {
                document.write('<div class="result error">');
                document.write('<h2>❌ 输入错误</h2>');
                document.write('请输入0-100之间的有效数字！<br>');
                document.write('你输入的是：<span class="highlight">' + score + '</span>');
                document.write('</div>');
            } else {
                // 显示输入的成绩
                document.write('<div class="result">');
                document.write('<h2>📊 输入的成绩</h2>');
                document.write('成绩：<span class="highlight">' + numScore + '</span> 分');
                document.write('</div>');

                // 方法1：使用switch判断等级（推荐方式）
                document.write('<div class="result success">');
                document.write('<h2>🏆 成绩等级（Switch方法1）</h2>');

                var grade;
                var level = Math.floor(numScore / 10); // 将分数转换为等级区间

                switch (level) {
                    case 10:
                    case 9:
                        grade = "A（优秀）";
                        break;
                    case 8:
                        grade = "B（良好）";
                        break;
                    case 7:
                        grade = "C（中等）";
                        break;
                    case 6:
                        grade = "D（及格）";
                        break;
                    default:
                        grade = "F（不及格）";
                        break;
                }

                document.write('等级：<span class="highlight">' + grade + '</span><br>');
                document.write('计算方式：Math.floor(' + numScore + ' / 10) = ' + level);
                document.write('</div>');

                // 方法2：使用switch判断合格/不合格（你原来想要的效果）
                document.write('<div class="result success">');
                document.write('<h2>✅ 合格判断（Switch方法2）</h2>');

                var result;
                var passLevel = numScore >= 60 ? "pass" : "fail";

                switch (passLevel) {
                    case "pass":
                        result = "合格 🎉";
                        break;
                    case "fail":
                        result = "不合格 😞";
                        break;
                    default:
                        result = "未知状态";
                        break;
                }

                document.write('结果：<span class="highlight">' + result + '</span><br>');
                document.write('判断逻辑：' + numScore + ' >= 60 ? "pass" : "fail" → "' + passLevel + '"');
                document.write('</div>');

                // 输出到控制台
                console.log("成绩：" + numScore);
                console.log("等级：" + grade);
                console.log("合格状态：" + result);
            }
        </script>

        <div class="code-section">
            <h2>📝 Switch语句知识点</h2>

            <h3>❌ 错误用法（你原来的代码）</h3>
            <div class="code-block">
switch (score) {
    case score >= 60:  // ❌ 错误！switch不能直接使用条件表达式
        console.log("合格");
        break;
    case score < 60:   // ❌ 错误！这样写语法不对
        console.log("不合格");
        break;
}
            </div>

            <h3>✅ 正确用法1：等级判断</h3>
            <div class="code-block">
var level = Math.floor(score / 10);
switch (level) {
    case 10:
    case 9:
        grade = "A（优秀）";
        break;
    case 8:
        grade = "B（良好）";
        break;
    // ... 更多case
    default:
        grade = "F（不及格）";
        break;
}
            </div>

            <h3>✅ 正确用法2：合格判断</h3>
            <div class="code-block">
var passLevel = score >= 60 ? "pass" : "fail";
switch (passLevel) {
    case "pass":
        result = "合格";
        break;
    case "fail":
        result = "不合格";
        break;
}
            </div>

            <h3>💡 Switch vs If 的选择</h3>
            <p><strong>使用Switch：</strong>当需要根据具体的值进行精确匹配时</p>
            <p><strong>使用If：</strong>当需要进行范围判断或复杂条件时</p>
            <div class="code-block">
// 更适合用if语句的情况
if (score >= 90) {
    grade = "A";
} else if (score >= 80) {
    grade = "B";
} else if (score >= 70) {
    grade = "C";
} else if (score >= 60) {
    grade = "D";
} else {
    grade = "F";
}
            </div>
        </div>
    </div>
</body>
</html>