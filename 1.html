<span class="size">123</span>
<a href="./2.html">跳转1</a> 
<a href="./3.html">跳转2</a> 

<head>
  <link
    rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
  />
</head>

<style>
  .size{
    font-size: 3.13rem;
    position: relative;
    display: inline-block;
  }

  .size::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 5px;
    background-color: red;
    bottom: -5px;
  }
  .size::after {
    content: '>';
  }

  .wrap {
    width: 500px;
    height: 2500px;
    border: 1px solid #000;
    position: relative; 
  }
  .wrap2 {
    width: 300px;
    height: 300px;
    margin: 100px;
    border: 1px solid #000;
    /* position: relative; */
  }
  .inside {
    width: 50px;
    height: 50px;
    background-color: pink;
    position: relative;
    left:  100px;
    top: 0;
  }
</style>


<div class="wrap">
  <div class="wrap2">
    <div class="inside animate__animated animate__pulse">1111</div>
  </div>
</div>